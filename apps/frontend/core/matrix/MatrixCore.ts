/**
 * 矩阵核心引擎
 * 🎯 核心价值：统一的矩阵处理引擎，数据驱动渲染，业务模式切换
 * 📦 功能范围：数据处理管道、渲染引擎、交互处理、性能优化
 * 🔄 架构设计：基于配置驱动的模块化架构，支持插件式业务模式
 */

import type {
  BusinessMode,
  CellData,
  CellRenderData,
  Coordinate,
  InteractionEvent,
  MatrixConfig,
  MatrixData,
  ModeHandler,
  ProcessedMatrixData
} from './MatrixTypes';

import {
  DEFAULT_COLOR_VALUES,
  getCachedGroupAData,
  getMatrixDataByCoordinate,
  toDisplayCoordinate
} from '../data/GroupAData';

import { coordinateKey } from './MatrixTypes';

// ===== 简化的样式工具函数 =====

/**
 * 统一的背景色函数 - 极简化设计
 * 只区分彩色背景和白色背景，保证视觉统一性
 */
const getBackgroundColor = (mode: BusinessMode, cellColor?: any): string => {
  // 颜色模式且有颜色数据时使用彩色背景
  if (mode === 'color' && cellColor && DEFAULT_COLOR_VALUES[cellColor as keyof typeof DEFAULT_COLOR_VALUES]) {
    return DEFAULT_COLOR_VALUES[cellColor as keyof typeof DEFAULT_COLOR_VALUES].hex;
  }
  // 其他情况统一使用白色背景
  return '#ffffff';
};

/**
 * 统一的文字颜色函数 - 极简化设计
 * 只有黑色背景用白色文字，其他背景用黑色文字
 */
const getTextColor = (backgroundColor: string): string => {
  // 判断是否为深色背景
  const isDarkBackground = backgroundColor === '#000000' ||
    (backgroundColor.startsWith('#') && parseInt(backgroundColor.slice(1), 16) < 0x808080);

  return isDarkBackground ? '#ffffff' : '#000000';
};

/**
 * 统一的边框函数 - 极简化设计
 * 所有单元格使用相同的现代化边框
 */
const getBorder = (): string => {
  return '1px solid #e0e0e0';
};

/**
 * 统一的字体大小函数 - 根据显示模式动态调整
 * 坐标模式使用9px，其他模式使用16px
 */
const getFontSize = (mode: BusinessMode): string => {
  return mode === 'coordinate' ? '9px' : '16px';
};

/**
 * 统一的单元格样式创建函数 - 极简化设计
 * 现代化的视觉效果，统一的样式规则
 */
const createCellStyle = (cell: CellData, mode: BusinessMode, matrixDataColor?: any) => {
  const cellColor = matrixDataColor || cell.color;
  const backgroundColor = getBackgroundColor(mode, cellColor);

  const style: any = {
    backgroundColor,
    border: getBorder(),
    borderRadius: '6px', // 现代化圆角
    color: getTextColor(backgroundColor),
    fontSize: getFontSize(mode), // 根据模式动态设置字体大小
    fontWeight: 'bold', // 统一加粗，确保可读性
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)', // 现代化阴影
    transition: 'all 0.2s ease', // 平滑过渡效果
  };

  return style;
};

/**
 * 统一的单元格类名创建函数 - 极简化设计
 * 简化类名逻辑，保持基本的交互状态
 */
const createCellClassName = (cell: CellData, mode: BusinessMode) => {
  const classes = ['matrix-cell', `${mode}-mode`];

  // 只保留基本的交互状态类名
  if (cell.isSelected) classes.push('selected');
  if (cell.isHovered) classes.push('hovered');

  return classes.join(' ');
};

const createCellContent = (cell: CellData, mode: BusinessMode) => {
  switch (mode) {
    case 'coordinate': {
      // 使用显示坐标而不是网格坐标
      const [displayX, displayY] = toDisplayCoordinate(cell.x, cell.y);
      return `${displayX},${displayY}`;
    }
    case 'color': return '';
    case 'level': return cell.level?.toString() || '';
    case 'word': return cell.word || '';
    default: return '';
  }
};

// ===== 业务模式处理器 =====

/** 坐标模式处理器 */
const coordinateModeHandler: ModeHandler = {
  processData: (data: MatrixData): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      renderData.set(key, {
        content: createCellContent(cell, 'coordinate'),
        style: createCellStyle(cell, 'coordinate'),
        className: createCellClassName(cell, 'coordinate'),
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'coordinate',
      },
    };
  },

  renderCell: (cell: CellData): CellRenderData => ({
    content: createCellContent(cell, 'coordinate'),
    style: createCellStyle(cell, 'coordinate'),
    className: createCellClassName(cell, 'coordinate'),
    isInteractive: true,
  }),

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Coordinate mode interaction: ${event.type} at (${cell.x}, ${cell.y})`);
  },
};

/** 颜色模式处理器 */
const colorModeHandler: ModeHandler = {
  processData: (data: MatrixData): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      // 检查是否有矩阵数据
      const groupAData = getCachedGroupAData();
      const matrixData = getMatrixDataByCoordinate(groupAData, cell.x, cell.y);
      const cellColor = matrixData?.color || cell.color;

      renderData.set(key, {
        content: createCellContent(cell, 'color'),
        style: createCellStyle(cell, 'color', cellColor),
        className: createCellClassName(cell, 'color'),
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'color',
      },
    };
  },

  renderCell: (cell: CellData): CellRenderData => {
    return {
      content: createCellContent(cell, 'color'),
      style: createCellStyle(cell, 'color'),
      className: createCellClassName(cell, 'color'),
      isInteractive: true,
    };
  },

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Color mode interaction: ${event.type} at (${cell.x}, ${cell.y}), color: ${cell.color}`);
  },
};

/** 等级模式处理器 */
const levelModeHandler: ModeHandler = {
  processData: (data: MatrixData): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      renderData.set(key, {
        content: createCellContent(cell, 'level'),
        style: createCellStyle(cell, 'level'),
        className: createCellClassName(cell, 'level'),
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'level',
      },
    };
  },

  renderCell: (cell: CellData): CellRenderData => ({
    content: createCellContent(cell, 'level'),
    style: createCellStyle(cell, 'level'),
    className: createCellClassName(cell, 'level'),
    isInteractive: true,
  }),

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Level mode interaction: ${event.type} at (${cell.x}, ${cell.y}), level: ${cell.level}`);
  },
};

/** 词语模式处理器 */
const wordModeHandler: ModeHandler = {
  processData: (data: MatrixData): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      renderData.set(key, {
        content: createCellContent(cell, 'word'),
        style: createCellStyle(cell, 'word'),
        className: createCellClassName(cell, 'word'),
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'word',
      },
    };
  },

  renderCell: (cell: CellData): CellRenderData => ({
    content: createCellContent(cell, 'word'),
    style: createCellStyle(cell, 'word'),
    className: createCellClassName(cell, 'word'),
    isInteractive: true,
  }),

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Word mode interaction: ${event.type} at (${cell.x}, ${cell.y}), word: ${cell.word}`);
  },
};

// ===== 模式处理器注册表 =====

const modeHandlers: Record<BusinessMode, ModeHandler> = {
  coordinate: coordinateModeHandler,
  color: colorModeHandler,
  level: levelModeHandler,
  word: wordModeHandler,
};

// ===== 矩阵核心引擎 =====

export class MatrixCore {
  /**
   * 处理矩阵数据 - 简化版本
   */
  processData(data: MatrixData, config: MatrixConfig): ProcessedMatrixData {
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    return handler.processData(data);
  }

  /**
   * 渲染单个单元格 - 简化版本
   */
  renderCell(cell: CellData, config: MatrixConfig): CellRenderData {
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    return handler.renderCell(cell);
  }

  /**
   * 处理交互事件 - 简化版本
   */
  handleInteraction(event: InteractionEvent, cell: CellData, config: MatrixConfig): void {
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    handler.handleInteraction(event, cell);
  }

  /**
   * 切换业务模式 - 简化版本
   */
  switchMode(mode: BusinessMode, data: MatrixData, config: MatrixConfig): ProcessedMatrixData {
    const newConfig = { ...config, mode };
    return this.processData(data, newConfig);
  }

  /**
   * 批量更新单元格 - 简化版本
   */
  batchUpdateCells(
    updates: Array<{ coordinate: Coordinate; data: Partial<CellData> }>,
    data: MatrixData
  ): MatrixData {
    const newData = { ...data };
    const newCells = new Map(data.cells);

    updates.forEach(({ coordinate, data: cellData }) => {
      const key = coordinateKey(coordinate.x, coordinate.y);
      const existingCell = newCells.get(key);

      if (existingCell) {
        newCells.set(key, { ...existingCell, ...cellData });
      }
    });

    newData.cells = newCells;
    return newData;
  }

  /**
   * 验证矩阵数据
   */
  validateData(data: MatrixData): boolean {
    // 检查数据完整性
    if (!data.cells || !(data.cells instanceof Map)) {
      return false;
    }

    // 检查单元格数据
    for (const [key, cell] of data.cells) {
      if (!this.validateCell(cell)) {
        console.warn(`Invalid cell data at ${key}:`, cell);
        return false;
      }
    }

    return true;
  }

  /**
   * 验证单元格数据
   */
  private validateCell(cell: CellData): boolean {
    return (
      typeof cell.x === 'number' &&
      typeof cell.y === 'number' &&
      cell.x >= 0 && cell.x < 33 &&
      cell.y >= 0 && cell.y < 33 &&
      typeof cell.isActive === 'boolean' &&
      typeof cell.isSelected === 'boolean' &&
      typeof cell.isHovered === 'boolean'
    );
  }
}

// ===== 单例实例 =====

export const matrixCore = new MatrixCore();

// ===== 工具函数 =====

/**
 * 创建交互事件
 */
export const createInteractionEvent = (
  type: InteractionEvent['type'],
  coordinate: Coordinate,
  modifiers: Partial<InteractionEvent['modifiers']> = {},
  data?: any
): InteractionEvent => ({
  type,
  coordinate,
  modifiers: {
    ctrl: false,
    shift: false,
    alt: false,
    ...modifiers,
  },
  data,
});

/**
 * 注册自定义模式处理器
 */
export const registerModeHandler = (mode: string, handler: ModeHandler): void => {
  (modeHandlers as any)[mode] = handler;
};

/**
 * 获取可用的业务模式
 */
export const getAvailableModes = (): BusinessMode[] => {
  return Object.keys(modeHandlers) as BusinessMode[];
};
