/**
 * 坐标系统演示脚本
 * 展示以(16,16)为中心点(0,0)的坐标模式
 */

import {
  MatrixDataManager,
  toDisplayCoordinate,
  fromDisplayCoordinate,
  validateCoordinateSystem,
  getDataPointCoordinateInfo
} from '../core/data/GroupAData';

import { MATRIX_SIZE } from '../core/matrix/MatrixTypes';

/**
 * 演示坐标系统的基本概念
 */
function demonstrateCoordinateSystem() {
  console.log('🎯 坐标系统演示');
  console.log('=====================================');
  
  const validation = validateCoordinateSystem();
  
  console.log('📊 坐标系统信息:');
  console.log(`  矩阵大小: ${MATRIX_SIZE}×${MATRIX_SIZE}`);
  console.log(`  网格坐标范围: (0,0) 到 (${MATRIX_SIZE-1},${MATRIX_SIZE-1})`);
  console.log(`  显示坐标范围: (-16,-16) 到 (16,16)`);
  console.log(`  中心点: 网格(16,16) = 显示(0,0)`);
  console.log(`  验证状态: ${validation.isValid ? '✅ 正常' : '❌ 异常'}`);
}

/**
 * 演示A组数据在新坐标系统中的分布
 */
function demonstrateGroupADistribution() {
  console.log('\n🎨 A组数据分布演示');
  console.log('=====================================');
  
  const groupAData = MatrixDataManager.getGroupAData();
  
  // 按颜色分组显示
  const colorGroups = new Map();
  groupAData.points.forEach(point => {
    if (!colorGroups.has(point.color)) {
      colorGroups.set(point.color, []);
    }
    colorGroups.get(point.color).push(point);
  });
  
  console.log('各颜色数据点的显示坐标:');
  
  for (const [color, points] of colorGroups) {
    console.log(`\n${color.toUpperCase()}:`);
    
    // 按级别分组
    const levelGroups = new Map();
    points.forEach(point => {
      if (!levelGroups.has(point.level)) {
        levelGroups.set(point.level, []);
      }
      levelGroups.get(point.level).push(point);
    });
    
    for (const [level, levelPoints] of levelGroups) {
      const coords = levelPoints.map(p => {
        const [dx, dy] = toDisplayCoordinate(p.x, p.y);
        return `(${dx},${dy})`;
      }).join(', ');
      console.log(`  L${level}: ${coords}`);
    }
  }
}

/**
 * 演示坐标查询功能
 */
function demonstrateCoordinateQuery() {
  console.log('\n🔍 坐标查询演示');
  console.log('=====================================');
  
  const groupAData = MatrixDataManager.getGroupAData();
  
  // 演示一些关键位置的查询
  const keyPositions = [
    { name: '中心点', coord: [0, 0] },
    { name: '正右', coord: [8, 0] },
    { name: '正左', coord: [-8, 0] },
    { name: '正下', coord: [0, 8] },
    { name: '正上', coord: [0, -8] },
    { name: '右下角', coord: [4, 4] },
    { name: '左上角', coord: [-4, -4] }
  ];
  
  console.log('关键位置查询结果:');
  keyPositions.forEach(({ name, coord }) => {
    const [x, y] = coord;
    const point = MatrixDataManager.getByDisplayCoordinate(groupAData, x, y);
    
    if (point) {
      const coordInfo = getDataPointCoordinateInfo(point);
      console.log(`  ${name} 显示(${x},${y}): ${point.color} L${point.level} [网格${coordInfo.grid.formatted}]`);
    } else {
      console.log(`  ${name} 显示(${x},${y}): 无数据`);
    }
  });
}

/**
 * 演示坐标转换的实际应用
 */
function demonstrateCoordinateConversion() {
  console.log('\n🔄 坐标转换演示');
  console.log('=====================================');
  
  console.log('从显示坐标到网格坐标的转换:');
  
  const displayCoords = [
    [0, 0],    // 中心
    [5, 3],    // 右下
    [-7, -2],  // 左上
    [16, 16],  // 右下角
    [-16, -16] // 左上角
  ];
  
  displayCoords.forEach(([dx, dy]) => {
    const [gx, gy] = fromDisplayCoordinate(dx, dy);
    const isValid = gx >= 0 && gx < MATRIX_SIZE && gy >= 0 && gy < MATRIX_SIZE;
    console.log(`  显示(${dx},${dy}) -> 网格(${gx},${gy}) ${isValid ? '✅' : '❌ 超出范围'}`);
  });
  
  console.log('\n从网格坐标到显示坐标的转换:');
  
  const gridCoords = [
    [16, 16],  // 中心
    [0, 0],    // 左上角
    [32, 32],  // 右下角
    [8, 24],   // 左下
    [24, 8]    // 右上
  ];
  
  gridCoords.forEach(([gx, gy]) => {
    const [dx, dy] = toDisplayCoordinate(gx, gy);
    console.log(`  网格(${gx},${gy}) -> 显示(${dx},${dy})`);
  });
}

/**
 * 生成坐标系统总结报告
 */
function generateCoordinateSystemSummary() {
  console.log('\n📋 坐标系统总结');
  console.log('=====================================');
  
  const groupAData = MatrixDataManager.getGroupAData();
  const validation = validateCoordinateSystem();
  
  console.log('系统配置:');
  console.log(`  ✓ 矩阵大小: ${MATRIX_SIZE}×${MATRIX_SIZE}`);
  console.log(`  ✓ 网格中心: (16,16)`);
  console.log(`  ✓ 显示中心: (0,0)`);
  console.log(`  ✓ 坐标验证: ${validation.isValid ? '通过' : '失败'}`);
  
  console.log('\n数据统计:');
  console.log(`  ✓ 总数据点: ${groupAData.points.length}`);
  console.log(`  ✓ 中心点数据: ${MatrixDataManager.getByDisplayCoordinate(groupAData, 0, 0) ? '有' : '无'}`);
  
  const stats = MatrixDataManager.getStatistics(groupAData);
  console.log(`  ✓ 网格利用率: ${(stats.densityAnalysis.gridUtilization * 100).toFixed(2)}%`);
  
  console.log('\n坐标范围:');
  console.log(`  ✓ 网格坐标: (0,0) 到 (32,32)`);
  console.log(`  ✓ 显示坐标: (-16,-16) 到 (16,16)`);
  
  console.log('\n功能特性:');
  console.log('  ✓ 双向坐标转换');
  console.log('  ✓ 显示坐标查询');
  console.log('  ✓ 坐标信息获取');
  console.log('  ✓ 坐标系统验证');
}

/**
 * 主演示函数
 */
function runCoordinateDemo() {
  demonstrateCoordinateSystem();
  demonstrateGroupADistribution();
  demonstrateCoordinateQuery();
  demonstrateCoordinateConversion();
  generateCoordinateSystemSummary();
  
  console.log('\n🎉 坐标系统演示完成！');
  console.log('\n💡 现在您可以使用以(16,16)为中心点(0,0)的坐标模式了！');
}

// 如果直接运行此脚本
if (require.main === module) {
  runCoordinateDemo();
}

export { runCoordinateDemo };
